import Plugin from 'src/plugin-system/plugin.class';

export default class BsUserManagementPlugin extends Plugin {
    /**
     * Default options for the class.
     */
    static options = {
        addContactModalSelector: '#addContactModal',
        addContactBtnSelector: '.add-contact-btn',
        removeContactBtnSelector: '.remove-contact-btn',
        saveContactBtnSelector: '#saveContactBtn',
        modalEmailSelector: '#modalEmail',
        modalCustomerIdSelector: '#modalCustomerId',
        contactPersonNameSelector: '#contactPersonName',
        addContactFormSelector: '#addContactForm',
        addContactUrl: null,
        removeContactUrl: null,
        translations: {
            pleaseEnterContactPerson: 'Please enter a contact person name',
            errorAddingContact: 'Error adding contact person',
            errorRemovingContact: 'Error removing contact person',
            confirmRemoveContact: 'Are you sure you want to remove this contact person?'
        }
    };

    /**
     * Initialize the plugin
     */
    init() {
        this._registerEventListeners();
    }

    /**
     * Register all event listeners
     */
    _registerEventListeners() {
        // Add contact person functionality
        const addContactBtns = document.querySelectorAll(this.options.addContactBtnSelector);
        addContactBtns.forEach(btn => {
            btn.addEventListener('click', this._onAddContactBtnClick.bind(this));
        });

        // Save contact person functionality
        const saveContactBtn = document.querySelector(this.options.saveContactBtnSelector);
        if (saveContactBtn) {
            saveContactBtn.addEventListener('click', this._onSaveContactBtnClick.bind(this));
        }

        // Remove contact person functionality
        // const removeContactBtns = document.querySelectorAll(this.options.removeContactBtnSelector);
        // removeContactBtns.forEach(btn => {
        //     btn.addEventListener('click', this._onRemoveContactBtnClick.bind(this));
        // });

        // Modal close functionality
        const modal = document.querySelector(this.options.addContactModalSelector);
        if (modal) {
            const closeBtns = modal.querySelectorAll('[data-bs-dismiss="modal"], .btn-close');
            closeBtns.forEach(btn => {
                btn.addEventListener('click', this._closeModal.bind(this));
            });
        }
    }

    /**
     * Handle add contact button click
     */
    _onAddContactBtnClick(event) {
        const btn = event.currentTarget;
        const modalEmail = document.querySelector(this.options.modalEmailSelector);
        const modalCustomerId = document.querySelector(this.options.modalCustomerIdSelector);
        const contactPersonName = document.querySelector(this.options.contactPersonNameSelector);
        const modal = document.querySelector(this.options.addContactModalSelector);

        if (modalEmail && modalCustomerId && contactPersonName) {
            modalEmail.value = btn.dataset.email;
            modalCustomerId.value = btn.dataset.customerId;
            contactPersonName.value = '';
        }

        // Show the modal programmatically
        if (modal) {
            // Try Bootstrap 5 first
            if (window.bootstrap && window.bootstrap.Modal) {
                const bootstrapModal = new window.bootstrap.Modal(modal);
                bootstrapModal.show();
            }
            // Fallback to Bootstrap 4 or jQuery
            else if (window.$ && window.$.fn.modal) {
                window.$(modal).modal('show');
            }
            // Last resort - manual show
            else {
                modal.style.display = 'block';
                modal.classList.add('show');
                modal.setAttribute('aria-hidden', 'false');

                // Add backdrop
                const backdrop = document.createElement('div');
                backdrop.className = 'modal-backdrop fade show';
                backdrop.id = 'modal-backdrop-' + Date.now();
                document.body.appendChild(backdrop);
                document.body.classList.add('modal-open');

                // Store backdrop reference for cleanup
                modal.setAttribute('data-backdrop-id', backdrop.id);
            }
        }
    }

    /**
     * Handle save contact button click
     */
    _onSaveContactBtnClick() {
        const modalCustomerId = document.querySelector(this.options.modalCustomerIdSelector);
        const contactPersonName = document.querySelector(this.options.contactPersonNameSelector);

        if (!modalCustomerId || !contactPersonName) {
            return;
        }

        const customerId = modalCustomerId.value;
        const contactPerson = contactPersonName.value.trim();

        if (!contactPerson) {
            alert(this.options.translations.pleaseEnterContactPerson);
            return;
        }

        if (!this.options.addContactUrl) {
            console.error('Add contact URL not configured');
            return;
        }

        this._addContactPerson(customerId, contactPerson);
    }

    /**
     * Close the modal
     */
    _closeModal() {
        const modal = document.querySelector(this.options.addContactModalSelector);
        if (!modal) return;

        // Try Bootstrap 5 first
        if (window.bootstrap && window.bootstrap.Modal) {
            const bootstrapModal = window.bootstrap.Modal.getInstance(modal);
            if (bootstrapModal) {
                bootstrapModal.hide();
            }
        }
        // Fallback to Bootstrap 4 or jQuery
        else if (window.$ && window.$.fn.modal) {
            window.$(modal).modal('hide');
        }
        // Manual hide
        else {
            modal.style.display = 'none';
            modal.classList.remove('show');
            modal.setAttribute('aria-hidden', 'true');

            // Remove backdrop
            const backdropId = modal.getAttribute('data-backdrop-id');
            if (backdropId) {
                const backdrop = document.getElementById(backdropId);
                if (backdrop) {
                    backdrop.remove();
                }
                modal.removeAttribute('data-backdrop-id');
            }
            document.body.classList.remove('modal-open');
        }
    }

    /**
     * Handle remove contact button click
     */
    _onRemoveContactBtnClick(event) {
        // if (confirm(this.options.translations.confirmRemoveContact)) {
        //     const btn = event.currentTarget;
        //     const contactId = btn.dataset.contactId;
        //
        //     if (!this.options.removeContactUrl) {
        //         console.error('Remove contact URL not configured');
        //         return;
        //     }
        //
        //     this._removeContactPerson(contactId);
        // }
    }

    /**
     * Add contact person via AJAX
     */
    _addContactPerson(customerId, contactPerson) {
        // Get CSRF token from the form or fallback to dedicated token
        const form = document.querySelector(this.options.addContactFormSelector);
        const csrfToken = form ? form.querySelector('input[name="_csrf_token"]') : document.getElementById('csrf-add-contact');

        const formData = new URLSearchParams({
            customerId: customerId,
            contactPerson: contactPerson
        });

        // Add CSRF token if available
        if (csrfToken && csrfToken.value) {
            formData.append('_csrf_token', csrfToken.value);
        }

        fetch(this.options.addContactUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this._closeModal();
                location.reload();
            } else {
                alert(data.message || this.options.translations.errorAddingContact);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert(this.options.translations.errorAddingContact);
        });
    }

    /**
     * Remove contact person via AJAX
     */
    _removeContactPerson(contactId) {
        // Get CSRF token for remove operation
        const csrfToken = document.getElementById('csrf-remove-contact');

        const formData = new URLSearchParams({
            contactPersonId: contactId
        });

        // Add CSRF token if available
        if (csrfToken && csrfToken.value) {
            formData.append('_csrf_token', csrfToken.value);
        }

        fetch(this.options.removeContactUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || this.options.translations.errorRemovingContact);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert(this.options.translations.errorRemovingContact);
        });
    }
}
