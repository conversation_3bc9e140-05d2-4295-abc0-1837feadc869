{"account": {"userManagement": {"title": "User Management", "description": "Manage contact persons for email addresses associated with your customer number.", "emailAddresses": "Email Addresses", "addContact": "Add Contact Person", "contactPersons": "Contact Persons", "removeContact": "Remove Contact Person", "noContactPersons": "No contact persons assigned to this email address.", "noEmailAddresses": "No email addresses found for your customer number.", "addContactPerson": "Add Contact Person", "emailAddress": "Email Address", "contactPersonName": "Contact Person Name", "pleaseEnterContactPerson": "Please enter a contact person name.", "errorAddingContact": "Error adding contact person. Please try again.", "confirmRemoveContact": "Are you sure you want to remove this contact person?", "confirmRemoveTitle": "Remove Contact Person", "errorRemovingContact": "Error removing contact person. Please try again.", "contactAddedSuccess": "Contact person has been added successfully.", "contactRemovedSuccess": "Contact person has been removed successfully."}, "order": {"contactPerson": "Contact Person"}}, "checkout": {"contactPerson": {"label": "Select Contact Person", "placeholder": "Choose a contact person...", "required": "Please select a contact person."}, "finish": {"contactPerson": "Contact Person"}}, "general": {"cancel": "Cancel", "save": "Save", "loading": "Loading..."}}