{% sw_extends '@Storefront/storefront/page/account/index.html.twig' %}

{% block page_account_main_content %}
    <div class="account-user-management"
         data-bs-user-management-plugin="true"
         data-bs-user-management-plugin-options='{
             "addContactUrl": "{{ path('frontend.account.user.management.add.contact') }}",
             "removeContactUrl": "{{ path('frontend.account.user.management.remove.contact') }}",
             "translations": {
                 "pleaseEnterContactPerson": "{{ "bs.orderContact.userManagement.pleaseEnterContactPerson"|trans|sw_sanitize }}",
                 "errorAddingContact": "{{ "bs.orderContact.userManagement.errorAddingContact"|trans|sw_sanitize }}",
                 "errorRemovingContact": "{{ "bs.orderContact.userManagement.errorRemovingContact"|trans|sw_sanitize }}",
                 "confirmRemoveContact": "{{ "bs.orderContact.userManagement.confirmRemoveContact"|trans|sw_sanitize }}"
             }
         }'>

        <!-- Hidden CSRF tokens for AJAX operations -->
        <div style="display: none;">
            {{ sw_csrf('frontend.account.user.management.add.contact', {'id': 'csrf-add-contact'}) }}
            {{ sw_csrf('frontend.account.user.management.remove.contact', {'id': 'csrf-remove-contact'}) }}
        </div>
        <div class="account-welcome">
            <h1>{{ "bs.orderContact.userManagement.title"|trans|sw_sanitize }}</h1>
            <p>{{ "bs.orderContact.userManagement.description"|trans|sw_sanitize }}</p>
        </div>

        {% if customers|length > 0 %}
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">{{ "bs.orderContact.userManagement.emailAddresses"|trans|sw_sanitize }}</h5>
                    
                    {% for customer in customers %}
                        <div class="user-management-email-section mb-4 p-3 border rounded">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="mb-2">
                                        <strong>{{ customer.email }}</strong>
                                        {% if customer.firstName and customer.lastName %}
                                            <small class="text-muted">({{ customer.firstName }} {{ customer.lastName }})</small>
                                        {% endif %}
                                    </h6>
                                </div>
                                <div class="col-md-6 text-md-right">
                                    <button type="button"
                                            class="btn btn-sm btn-primary add-contact-btn"
                                            data-customer-id="{{ customer.id }}"
                                            data-email="{{ customer.email }}"
                                            data-bs-toggle="modal"
                                            data-bs-target="#addContactModal">
                                        {{ "bs.orderContact.userManagement.addContact"|trans|sw_sanitize }}
                                    </button>
                                </div>
                            </div>

                            <div class="contact-persons-list mt-3">
                                <h6>{{ "bs.orderContact.userManagement.contactPersons"|trans|sw_sanitize }}:</h6>
                                {% if customer.contactPersons|length > 0 %}
                                    <div class="row">
                                        {% for contact in customer.contactPersons %}
                                            <div class="col-md-6 mb-2">
                                                <div class="contact-person-item d-flex justify-content-between align-items-center p-2 bg-light rounded">
                                                    <span>{{ contact.contactPerson }}</span>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-danger remove-contact-btn"
                                                            data-contact-id="{{ contact.id }}"
                                                            title="{{ "bs.orderContact.userManagement.removeContact"|trans|sw_sanitize }}">
                                                        {% sw_icon 'trash' %}
                                                    </button>
                                                </div>
                                            </div>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    <p class="text-muted">{{ "bs.orderContact.userManagement.noContactPersons"|trans|sw_sanitize }}</p>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% else %}
            <div class="alert alert-info">
                {{ "bs.orderContact.userManagement.noEmailAddresses"|trans|sw_sanitize }}
            </div>
        {% endif %}
    </div>

    <!-- Add Contact Modal -->
    <div class="modal fade" id="addContactModal" tabindex="-1" aria-labelledby="addContactModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addContactModalLabel">{{ "bs.orderContact.userManagement.addContactPerson"|trans|sw_sanitize }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addContactForm">
                        {{ sw_csrf('frontend.account.user.management.add.contact') }}
                        <div class="mb-3">
                            <label for="modalEmail" class="form-label">{{ "bs.orderContact.userManagement.emailAddress"|trans|sw_sanitize }}</label>
                            <input type="email" class="form-control" id="modalEmail" readonly>
                            <input type="hidden" id="modalCustomerId">
                        </div>
                        <div class="mb-3">
                            <label for="contactPersonName" class="form-label">{{ "bs.orderContact.userManagement.contactPersonName"|trans|sw_sanitize }}</label>
                            <input type="text" class="form-control" id="contactPersonName" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ "general.cancel"|trans|sw_sanitize }}</button>
                    <button type="button" class="btn btn-primary" id="saveContactBtn">{{ "general.save"|trans|sw_sanitize }}</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Remove Contact Confirmation Modal -->
    <div class="modal fade" id="removeContactModal" tabindex="-1" aria-labelledby="removeContactModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="removeContactModalLabel">{{ "bs.orderContact.userManagement.confirmRemoveTitle"|trans|sw_sanitize }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>{{ "bs.orderContact.userManagement.confirmRemoveContact"|trans|sw_sanitize }}</p>
                    <p><strong id="contactPersonToRemove"></strong></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ "general.cancel"|trans|sw_sanitize }}</button>
                    <button type="button" class="btn btn-danger" id="confirmRemoveContactBtn">{{ "bs.orderContact.userManagement.removeContact"|trans|sw_sanitize }}</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let currentContactIdToRemove = null;

            // Handle add contact button clicks
            const addContactBtns = document.querySelectorAll('.add-contact-btn');
            addContactBtns.forEach(function(btn) {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();

                    const modal = document.getElementById('addContactModal');
                    const modalEmail = document.getElementById('modalEmail');
                    const modalCustomerId = document.getElementById('modalCustomerId');
                    const contactPersonName = document.getElementById('contactPersonName');

                    // Populate modal fields
                    if (modalEmail && modalCustomerId && contactPersonName) {
                        modalEmail.value = this.dataset.email;
                        modalCustomerId.value = this.dataset.customerId;
                        contactPersonName.value = '';
                    }

                    // Show modal
                    if (modal) {
                        // Try Bootstrap 5 first
                        if (window.bootstrap && window.bootstrap.Modal) {
                            const bootstrapModal = new window.bootstrap.Modal(modal);
                            bootstrapModal.show();
                        }
                        // Fallback to Bootstrap 4 or jQuery
                        else if (window.$ && window.$.fn.modal) {
                            $(modal).modal('show');
                        }
                        // Manual show as last resort
                        else {
                            modal.style.display = 'block';
                            modal.classList.add('show');
                            modal.setAttribute('aria-hidden', 'false');
                            document.body.classList.add('modal-open');

                            // Add backdrop
                            const backdrop = document.createElement('div');
                            backdrop.className = 'modal-backdrop fade show';
                            backdrop.onclick = function() {
                                modal.style.display = 'none';
                                modal.classList.remove('show');
                                modal.setAttribute('aria-hidden', 'true');
                                document.body.classList.remove('modal-open');
                                backdrop.remove();
                            };
                            document.body.appendChild(backdrop);
                        }
                    }
                });
            });

            // Handle save contact button
            const saveContactBtn = document.getElementById('saveContactBtn');
            if (saveContactBtn) {
                saveContactBtn.addEventListener('click', function() {
                    const modalCustomerId = document.getElementById('modalCustomerId');
                    const contactPersonName = document.getElementById('contactPersonName');
                    const form = document.getElementById('addContactForm');
                    const csrfToken = form ? form.querySelector('input[name="_csrf_token"]') : document.getElementById('csrf-add-contact');

                    if (!modalCustomerId || !contactPersonName) {
                        return;
                    }

                    const customerId = modalCustomerId.value;
                    const contactPerson = contactPersonName.value.trim();

                    if (!contactPerson) {
                        showFlashMessage('danger', '{{ "bs.orderContact.userManagement.pleaseEnterContactPerson"|trans|sw_sanitize }}');
                        return;
                    }

                    const formData = new URLSearchParams({
                        customerId: customerId,
                        contactPerson: contactPerson
                    });

                    // Add CSRF token if available
                    if (csrfToken && csrfToken.value) {
                        formData.append('_csrf_token', csrfToken.value);
                    }

                    // Show loading state
                    saveContactBtn.disabled = true;
                    saveContactBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> {{ "general.loading"|trans|sw_sanitize }}';

                    fetch('{{ path('frontend.account.user.management.add.contact') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Close modal
                            const modal = document.getElementById('addContactModal');
                            if (modal) {
                                modal.style.display = 'none';
                                modal.classList.remove('show');
                                modal.setAttribute('aria-hidden', 'true');
                                document.body.classList.remove('modal-open');

                                // Remove backdrop
                                const backdrop = document.querySelector('.modal-backdrop');
                                if (backdrop) {
                                    backdrop.remove();
                                }
                            }
                            showFlashMessage('success', data.message || '{{ "bs.orderContact.userManagement.contactAddedSuccess"|trans|sw_sanitize }}');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showFlashMessage('danger', data.message || '{{ "bs.orderContact.userManagement.errorAddingContact"|trans|sw_sanitize }}');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showFlashMessage('danger', '{{ "bs.orderContact.userManagement.errorAddingContact"|trans|sw_sanitize }}');
                    })
                    .finally(() => {
                        // Reset button state
                        saveContactBtn.disabled = false;
                        saveContactBtn.innerHTML = '{{ "general.save"|trans|sw_sanitize }}';
                    });
                });
            }

            // Handle remove contact buttons
            const removeContactBtns = document.querySelectorAll('.remove-contact-btn');
            removeContactBtns.forEach(function(btn) {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();

                    currentContactIdToRemove = this.dataset.contactId;
                    const contactPersonName = this.closest('.contact-person-item').querySelector('span').textContent;

                    // Show confirmation modal
                    const modal = document.getElementById('removeContactModal');
                    const contactPersonToRemove = document.getElementById('contactPersonToRemove');

                    if (contactPersonToRemove) {
                        contactPersonToRemove.textContent = contactPersonName;
                    }

                    if (modal) {
                        // Try Bootstrap 5 first
                        if (window.bootstrap && window.bootstrap.Modal) {
                            const bootstrapModal = new window.bootstrap.Modal(modal);
                            bootstrapModal.show();
                        }
                        // Fallback to Bootstrap 4 or jQuery
                        else if (window.$ && window.$.fn.modal) {
                            $(modal).modal('show');
                        }
                        // Manual show as last resort
                        else {
                            modal.style.display = 'block';
                            modal.classList.add('show');
                            modal.setAttribute('aria-hidden', 'false');
                            document.body.classList.add('modal-open');

                            // Add backdrop
                            const backdrop = document.createElement('div');
                            backdrop.className = 'modal-backdrop fade show';
                            backdrop.onclick = function() {
                                modal.style.display = 'none';
                                modal.classList.remove('show');
                                modal.setAttribute('aria-hidden', 'true');
                                document.body.classList.remove('modal-open');
                                backdrop.remove();
                            };
                            document.body.appendChild(backdrop);
                        }
                    }
                });
            });

            // Handle confirm remove contact button
            const confirmRemoveContactBtn = document.getElementById('confirmRemoveContactBtn');
            if (confirmRemoveContactBtn) {
                confirmRemoveContactBtn.addEventListener('click', function() {
                    if (!currentContactIdToRemove) return;

                    const csrfToken = document.getElementById('csrf-remove-contact');

                    const formData = new URLSearchParams({
                        contactPersonId: currentContactIdToRemove
                    });

                    // Add CSRF token if available
                    if (csrfToken && csrfToken.value) {
                        formData.append('_csrf_token', csrfToken.value);
                    }

                    // Show loading state
                    confirmRemoveContactBtn.disabled = true;
                    confirmRemoveContactBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> {{ "general.loading"|trans|sw_sanitize }}';

                    fetch('{{ path('frontend.account.user.management.remove.contact') }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        // Close modal
                        const modal = document.getElementById('removeContactModal');
                        if (modal) {
                            modal.style.display = 'none';
                            modal.classList.remove('show');
                            modal.setAttribute('aria-hidden', 'true');
                            document.body.classList.remove('modal-open');

                            // Remove backdrop
                            const backdrop = document.querySelector('.modal-backdrop');
                            if (backdrop) {
                                backdrop.remove();
                            }
                        }

                        if (data.success) {
                            showFlashMessage('success', data.message || '{{ "bs.orderContact.userManagement.contactRemovedSuccess"|trans|sw_sanitize }}');
                            setTimeout(() => location.reload(), 1000);
                        } else {
                            showFlashMessage('danger', data.message || '{{ "bs.orderContact.userManagement.errorRemovingContact"|trans|sw_sanitize }}');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        showFlashMessage('danger', '{{ "bs.orderContact.userManagement.errorRemovingContact"|trans|sw_sanitize }}');

                        // Close modal on error
                        const modal = document.getElementById('removeContactModal');
                        if (modal) {
                            modal.style.display = 'none';
                            modal.classList.remove('show');
                            modal.setAttribute('aria-hidden', 'true');
                            document.body.classList.remove('modal-open');

                            const backdrop = document.querySelector('.modal-backdrop');
                            if (backdrop) {
                                backdrop.remove();
                            }
                        }
                    })
                    .finally(() => {
                        // Reset button state
                        confirmRemoveContactBtn.disabled = false;
                        confirmRemoveContactBtn.innerHTML = '{{ "bs.orderContact.userManagement.removeContact"|trans|sw_sanitize }}';
                        currentContactIdToRemove = null;
                    });
                });
            }

            // Handle modal close buttons
            const closeBtns = document.querySelectorAll('.modal [data-bs-dismiss="modal"], .modal .btn-close');
            closeBtns.forEach(function(btn) {
                btn.addEventListener('click', function() {
                    const modal = this.closest('.modal');
                    if (modal) {
                        modal.style.display = 'none';
                        modal.classList.remove('show');
                        modal.setAttribute('aria-hidden', 'true');
                        document.body.classList.remove('modal-open');

                        // Remove backdrop
                        const backdrop = document.querySelector('.modal-backdrop');
                        if (backdrop) {
                            backdrop.remove();
                        }
                    }
                });
            });

            // Flash message system
            function showFlashMessage(type, message) {
                // Remove existing flash messages
                const existingFlashes = document.querySelectorAll('.alert.flash-message');
                existingFlashes.forEach(flash => flash.remove());

                // Create new flash message
                const flashContainer = document.createElement('div');
                flashContainer.className = `alert alert-${type} alert-dismissible fade show flash-message`;
                flashContainer.style.position = 'fixed';
                flashContainer.style.top = '20px';
                flashContainer.style.right = '20px';
                flashContainer.style.zIndex = '9999';
                flashContainer.style.minWidth = '300px';
                flashContainer.style.maxWidth = '500px';

                flashContainer.innerHTML = `
                    <div class="d-flex align-items-center">
                        <div class="me-2">
                            ${type === 'success' ? '<i class="fas fa-check-circle"></i>' : '<i class="fas fa-exclamation-triangle"></i>'}
                        </div>
                        <div class="flex-grow-1">${message}</div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;

                document.body.appendChild(flashContainer);

                // Auto-remove after 5 seconds
                setTimeout(() => {
                    if (flashContainer.parentNode) {
                        flashContainer.remove();
                    }
                }, 5000);

                // Handle close button
                const closeBtn = flashContainer.querySelector('.btn-close');
                if (closeBtn) {
                    closeBtn.addEventListener('click', () => {
                        flashContainer.remove();
                    });
                }
            }
        });
    </script>
{% endblock %}

{% block page_account_main_content_inner %}{% endblock %}


