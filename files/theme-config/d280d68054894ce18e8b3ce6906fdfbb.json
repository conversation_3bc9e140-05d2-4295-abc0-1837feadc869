{"extensions": [], "themeConfig": {"blocks": {"themeColors": {"label": {"en-GB": "Theme colours", "de-DE": "Theme-<PERSON><PERSON>"}}, "typography": {"label": {"en-GB": "Typography", "de-DE": "<PERSON><PERSON><PERSON><PERSON>"}}, "eCommerce": {"label": {"en-GB": "E-Commerce", "de-DE": "E-Commerce"}}, "statusColors": {"label": {"en-GB": "Status messages", "de-DE": "Status-Ausgaben"}}, "media": {"label": {"en-GB": "Media", "de-DE": "Medien"}}, "unordered": {"label": {"en-GB": "Misc", "de-DE": "Sonstige"}}}, "fields": {"sw-color-brand-primary": {"label": {"en-GB": "Primary colour", "de-DE": "Primärfarbe"}, "type": "color", "value": "#008490", "editable": true, "block": "themeColors", "order": 100}, "sw-color-brand-secondary": {"label": {"en-GB": "Secondary colour", "de-DE": "Sekundärfarbe"}, "type": "color", "value": "#526e7f", "editable": true, "block": "themeColors", "order": 200}, "sw-border-color": {"label": {"en-GB": "Border", "de-DE": "<PERSON><PERSON><PERSON>"}, "type": "color", "value": "#bcc1c7", "editable": true, "block": "themeColors", "order": 300}, "sw-background-color": {"label": {"en-GB": "Background", "de-DE": "Hi<PERSON>grund"}, "type": "color", "value": "#fff", "editable": true, "block": "themeColors", "order": 400}, "sw-color-success": {"label": {"en-GB": "Success", "de-DE": "Erfolg"}, "type": "color", "value": "#3cc261", "editable": true, "block": "statusColors", "order": 100}, "sw-color-info": {"label": {"en-GB": "Information", "de-DE": "Information"}, "type": "color", "value": "#26b6cf", "editable": true, "block": "statusColors", "order": 200}, "sw-color-warning": {"label": {"en-GB": "Notice", "de-DE": "<PERSON><PERSON><PERSON><PERSON>"}, "type": "color", "value": "#ffbd5d", "editable": true, "block": "statusColors", "order": 300}, "sw-color-danger": {"label": {"en-GB": "Error", "de-DE": "<PERSON><PERSON>"}, "type": "color", "value": "#e52427", "editable": true, "block": "statusColors", "order": 400}, "sw-font-family-base": {"label": {"en-GB": "Fonttype text", "de-DE": "Schriftart Text"}, "type": "fontFamily", "value": "'Inter', sans-serif", "editable": true, "block": "typography", "order": 100}, "sw-text-color": {"label": {"en-GB": "Text colour", "de-DE": "Textfarbe"}, "type": "color", "value": "#4a545b", "editable": true, "block": "typography", "order": 200}, "sw-font-family-headline": {"label": {"en-GB": "Fonttype headline", "de-DE": "Schriftart Überschrift"}, "type": "fontFamily", "value": "'Inter', sans-serif", "editable": true, "block": "typography", "order": 300}, "sw-headline-color": {"label": {"en-GB": "Headline colour", "de-DE": "Überschriftfarbe"}, "type": "color", "value": "#4a545b", "editable": true, "block": "typography", "order": 400}, "sw-color-price": {"label": {"en-GB": "Price", "de-DE": "Pre<PERSON>"}, "type": "color", "value": "#4a545b", "editable": true, "block": "eCommerce", "order": 100}, "sw-color-buy-button": {"label": {"en-GB": "Buy button", "de-DE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type": "color", "value": "#008490", "editable": true, "block": "eCommerce", "order": 200}, "sw-color-buy-button-text": {"label": {"en-GB": "Buy button text", "de-DE": "<PERSON><PERSON><PERSON>-Button Text"}, "type": "color", "value": "#fff", "editable": true, "block": "eCommerce", "order": 300}, "sw-logo-desktop": {"label": {"en-GB": "Desktop", "de-DE": "Desktop"}, "helpText": {"en-GB": "Displayed on viewport sizes above 991px and as a fallback on smaller viewports, if no other logo is set.", "de-DE": "Wird bei Ansichten über 991px angezeigt und als Alternative bei kleineren Auflösungen, für die kein anderes Logo eingestellt ist."}, "type": "media", "value": "http://shopware6419.test/media/07/cd/ae/1732014506/demostore-logo.png", "editable": true, "block": "media", "order": 100, "fullWidth": true}, "sw-logo-tablet": {"label": {"en-GB": "Tablet", "de-DE": "Tablet"}, "helpText": {"en-GB": "Displayed between a viewport of 767px to 991px", "de-DE": "Wird zwischen einem viewport von 767px bis 991px angezeigt"}, "type": "media", "value": "http://shopware6419.test/media/07/cd/ae/1732014506/demostore-logo.png", "editable": true, "block": "media", "order": 200, "fullWidth": true}, "sw-logo-mobile": {"label": {"en-GB": "Mobile", "de-DE": "Mobil"}, "helpText": {"en-GB": "Displayed up to a viewport of 767px", "de-DE": "Wird bis zu einem Viewport von 767px angezeigt"}, "type": "media", "value": "http://shopware6419.test/media/07/cd/ae/1732014506/demostore-logo.png", "editable": true, "block": "media", "order": 300, "fullWidth": true}, "sw-logo-share": {"label": {"en-GB": "App & share icon", "de-DE": "App- & Share-Icon"}, "type": "media", "value": "", "editable": true, "block": "media", "order": 400}, "sw-logo-favicon": {"label": {"en-GB": "Favicon", "de-DE": "Favicon"}, "type": "media", "value": "http://shopware6419.test/media/77/a4/18/1732014506/favicon.png", "editable": true, "block": "media", "order": 500}}, "sw-color-brand-primary": {"extensions": [], "name": "sw-color-brand-primary", "label": {"en-GB": "Primary colour", "de-DE": "Primärfarbe"}, "helpText": null, "type": "color", "value": "#008490", "editable": true, "block": "themeColors", "section": null, "tab": null, "order": 100, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-color-brand-secondary": {"extensions": [], "name": "sw-color-brand-secondary", "label": {"en-GB": "Secondary colour", "de-DE": "Sekundärfarbe"}, "helpText": null, "type": "color", "value": "#526e7f", "editable": true, "block": "themeColors", "section": null, "tab": null, "order": 200, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-border-color": {"extensions": [], "name": "sw-border-color", "label": {"en-GB": "Border", "de-DE": "<PERSON><PERSON><PERSON>"}, "helpText": null, "type": "color", "value": "#bcc1c7", "editable": true, "block": "themeColors", "section": null, "tab": null, "order": 300, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-background-color": {"extensions": [], "name": "sw-background-color", "label": {"en-GB": "Background", "de-DE": "Hi<PERSON>grund"}, "helpText": null, "type": "color", "value": "#fff", "editable": true, "block": "themeColors", "section": null, "tab": null, "order": 400, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-color-success": {"extensions": [], "name": "sw-color-success", "label": {"en-GB": "Success", "de-DE": "Erfolg"}, "helpText": null, "type": "color", "value": "#3cc261", "editable": true, "block": "statusColors", "section": null, "tab": null, "order": 100, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-color-info": {"extensions": [], "name": "sw-color-info", "label": {"en-GB": "Information", "de-DE": "Information"}, "helpText": null, "type": "color", "value": "#26b6cf", "editable": true, "block": "statusColors", "section": null, "tab": null, "order": 200, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-color-warning": {"extensions": [], "name": "sw-color-warning", "label": {"en-GB": "Notice", "de-DE": "<PERSON><PERSON><PERSON><PERSON>"}, "helpText": null, "type": "color", "value": "#ffbd5d", "editable": true, "block": "statusColors", "section": null, "tab": null, "order": 300, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-color-danger": {"extensions": [], "name": "sw-color-danger", "label": {"en-GB": "Error", "de-DE": "<PERSON><PERSON>"}, "helpText": null, "type": "color", "value": "#e52427", "editable": true, "block": "statusColors", "section": null, "tab": null, "order": 400, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-font-family-base": {"extensions": [], "name": "sw-font-family-base", "label": {"en-GB": "Fonttype text", "de-DE": "Schriftart Text"}, "helpText": null, "type": "fontFamily", "value": "'Inter', sans-serif", "editable": true, "block": "typography", "section": null, "tab": null, "order": 100, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-text-color": {"extensions": [], "name": "sw-text-color", "label": {"en-GB": "Text colour", "de-DE": "Textfarbe"}, "helpText": null, "type": "color", "value": "#4a545b", "editable": true, "block": "typography", "section": null, "tab": null, "order": 200, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-font-family-headline": {"extensions": [], "name": "sw-font-family-headline", "label": {"en-GB": "Fonttype headline", "de-DE": "Schriftart Überschrift"}, "helpText": null, "type": "fontFamily", "value": "'Inter', sans-serif", "editable": true, "block": "typography", "section": null, "tab": null, "order": 300, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-headline-color": {"extensions": [], "name": "sw-headline-color", "label": {"en-GB": "Headline colour", "de-DE": "Überschriftfarbe"}, "helpText": null, "type": "color", "value": "#4a545b", "editable": true, "block": "typography", "section": null, "tab": null, "order": 400, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-color-price": {"extensions": [], "name": "sw-color-price", "label": {"en-GB": "Price", "de-DE": "Pre<PERSON>"}, "helpText": null, "type": "color", "value": "#4a545b", "editable": true, "block": "eCommerce", "section": null, "tab": null, "order": 100, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-color-buy-button": {"extensions": [], "name": "sw-color-buy-button", "label": {"en-GB": "Buy button", "de-DE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "helpText": null, "type": "color", "value": "#008490", "editable": true, "block": "eCommerce", "section": null, "tab": null, "order": 200, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-color-buy-button-text": {"extensions": [], "name": "sw-color-buy-button-text", "label": {"en-GB": "Buy button text", "de-DE": "<PERSON><PERSON><PERSON>-Button Text"}, "helpText": null, "type": "color", "value": "#fff", "editable": true, "block": "eCommerce", "section": null, "tab": null, "order": 300, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-logo-desktop": {"extensions": [], "name": "sw-logo-desktop", "label": {"en-GB": "Desktop", "de-DE": "Desktop"}, "helpText": {"en-GB": "Displayed on viewport sizes above 991px and as a fallback on smaller viewports, if no other logo is set.", "de-DE": "Wird bei Ansichten über 991px angezeigt und als Alternative bei kleineren Auflösungen, für die kein anderes Logo eingestellt ist."}, "type": "media", "value": "http://shopware6419.test/media/07/cd/ae/1732014506/demostore-logo.png", "editable": true, "block": "media", "section": null, "tab": null, "order": 100, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": true}, "sw-logo-tablet": {"extensions": [], "name": "sw-logo-tablet", "label": {"en-GB": "Tablet", "de-DE": "Tablet"}, "helpText": {"en-GB": "Displayed between a viewport of 767px to 991px", "de-DE": "Wird zwischen einem viewport von 767px bis 991px angezeigt"}, "type": "media", "value": "http://shopware6419.test/media/07/cd/ae/1732014506/demostore-logo.png", "editable": true, "block": "media", "section": null, "tab": null, "order": 200, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": true}, "sw-logo-mobile": {"extensions": [], "name": "sw-logo-mobile", "label": {"en-GB": "Mobile", "de-DE": "Mobil"}, "helpText": {"en-GB": "Displayed up to a viewport of 767px", "de-DE": "Wird bis zu einem Viewport von 767px angezeigt"}, "type": "media", "value": "http://shopware6419.test/media/07/cd/ae/1732014506/demostore-logo.png", "editable": true, "block": "media", "section": null, "tab": null, "order": 300, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": true}, "sw-logo-share": {"extensions": [], "name": "sw-logo-share", "label": {"en-GB": "App & share icon", "de-DE": "App- & Share-Icon"}, "helpText": null, "type": "media", "value": "", "editable": true, "block": "media", "section": null, "tab": null, "order": 400, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}, "sw-logo-favicon": {"extensions": [], "name": "sw-logo-favicon", "label": {"en-GB": "Favicon", "de-DE": "Favicon"}, "helpText": null, "type": "media", "value": "http://shopware6419.test/media/77/a4/18/1732014506/favicon.png", "editable": true, "block": "media", "section": null, "tab": null, "order": 500, "sectionOrder": null, "blockOrder": null, "tabOrder": null, "custom": null, "scss": null, "fullWidth": null}}, "technicalName": "Storefront", "name": "Shopware default theme", "previewMedia": "vendor/shopware/storefront/Resources/app/storefront/dist/assets/defaultThemePreview.jpg", "author": "shopware AG", "isTheme": true, "styleFiles": [{"extensions": [], "filepath": "vendor/shopware/storefront/Resources/app/storefront/src/scss/base.scss", "resolveMapping": {"vendor": "vendor/shopware/storefront/Resources/app/storefront/vendor"}}, {"extensions": [], "filepath": "vendor/shopware/storefront/Resources/app/storefront/src/scss/skin/shopware/_base.scss", "resolveMapping": {"vendor": "vendor/shopware/storefront/Resources/app/storefront/vendor"}}, {"extensions": [], "filepath": "@Plugins", "resolveMapping": []}], "scriptFiles": [{"extensions": [], "filepath": "vendor/shopware/storefront/Resources/app/storefront/dist/js/vendor-node.js", "resolveMapping": []}, {"extensions": [], "filepath": "vendor/shopware/storefront/Resources/app/storefront/dist/js/vendor-shared.js", "resolveMapping": []}, {"extensions": [], "filepath": "vendor/shopware/storefront/Resources/app/storefront/dist/js/runtime.js", "resolveMapping": []}, {"extensions": [], "filepath": "vendor/shopware/storefront/Resources/app/storefront/dist/storefront/js/storefront.js", "resolveMapping": []}, {"extensions": [], "filepath": "@Plugins", "resolveMapping": []}], "storefrontEntryFilepath": "vendor/shopware/storefront/Resources/app/storefront/src/main.js", "basePath": "vendor/shopware/storefront/Resources", "assetPaths": ["vendor/shopware/storefront/Resources/app/storefront/dist/assets"], "viewInheritance": [], "iconSets": []}